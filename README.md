# TP2 - Application Panier Magasin

## Description
Application Android de gestion de panier d'achat développée dans le cadre du cours 420-14D-FX Développement pour appareils mobiles.

L'application permet de :
- Parcourir une liste d'articles en magasin
- Ajouter des articles au panier avec quantité personnalisée
- Gérer le contenu du panier (modifier quantités, supprimer articles)
- Mode administrateur pour gérer les articles du magasin
- Navigation par onglets entre Magasin et Panier

## Fonctionnalités implémentées

### ✅ Layouts et Interface
- [x] Layout principal avec navigation par onglets (TabLayout + ViewPager2)
- [x] Fragment Magasin avec RecyclerView pour les articles
- [x] Fragment Panier avec RecyclerView pour les articles sélectionnés
- [x] Layouts d'items complexes pour le magasin (image, nom, description, prix, catégorie)
- [x] Layouts d'items simples pour le panier (nom, quantité, total)
- [x] Dialogues pour sélection de quantité et gestion d'articles
- [x] Menu principal et menu contextuel
- [x] FloatingActionButton pour ajout d'articles (mode admin)

### ✅ Modèles de données
- [x] Classe Item avec propriétés (nom, description, prix, catégorie, quantité)
- [x] Classe ItemPanier pour gérer les articles dans le panier
- [x] Enum Categorie pour les catégories d'articles
- [x] Utilitaires pour formatage des prix et calculs

### ✅ Ressources
- [x] Icônes vectorielles pour toutes les actions
- [x] Images par catégorie (Épicerie, Fruits, Légumes, Boissons, Viandes)
- [x] Backgrounds pour les chips de catégorie et prix
- [x] Strings externalisées pour l'internationalisation
- [x] Menus XML (principal et contextuel)

### 🚧 En cours de développement
- [ ] Implémentation complète des fragments (en attente du cours sur les fragments)
- [ ] Base de données SQLite avec Room
- [ ] Adapters pour les RecyclerViews
- [ ] Gestion complète des événements de clic
- [ ] Dialogues fonctionnels
- [ ] Persistance des données

## Structure du projet

```
app/src/main/
├── java/com/example/tp2_divin_koffi_sogbadji/
│   ├── MainActivity.kt                 # Activité principale
│   ├── models/
│   │   └── Item.kt                    # Modèles de données
│   ├── fragments/
│   │   ├── BaseFragment.kt            # Fragment de base
│   │   ├── MagasinFragment.kt         # Fragment liste magasin
│   │   └── PanierFragment.kt          # Fragment panier
│   └── utils/
│       └── DataUtils.kt               # Utilitaires et données de test
├── res/
│   ├── layout/
│   │   ├── activity_main.xml          # Layout principal
│   │   ├── fragment_magasin.xml       # Layout fragment magasin
│   │   ├── fragment_panier.xml        # Layout fragment panier
│   │   ├── item_magasin.xml           # Layout item magasin
│   │   ├── item_panier.xml            # Layout item panier
│   │   ├── dialog_quantite.xml        # Dialogue sélection quantité
│   │   └── dialog_ajouter_item.xml    # Dialogue ajout/modification item
│   ├── drawable/                      # Icônes et images
│   ├── menu/                          # Menus XML
│   └── values/
│       └── strings.xml                # Chaînes de caractères
```

## Configuration technique

- **API minimale** : 24 (Android 7.0)
- **API cible** : 36
- **Langage** : Kotlin
- **Architecture** : Fragments + ViewPager2 + TabLayout
- **View Binding** : Activé
- **Base de données** : SQLite avec Room (à implémenter)

## Prochaines étapes

1. **Apprentissage des fragments** : Attendre le cours sur les fragments pour implémenter la navigation
2. **Base de données** : Implémenter SQLite avec Room pour la persistance
3. **Adapters** : Créer les adapters pour les RecyclerViews
4. **Fonctionnalités** : Implémenter les dialogues et la gestion des événements
5. **Tests** : Ajouter des tests unitaires et d'interface

## Notes de développement

Ce projet a été structuré pour faciliter l'ajout des fonctionnalités manquantes une fois les concepts de fragments enseignés en cours. Tous les layouts, modèles de données et ressources sont prêts pour l'implémentation complète.

Les fragments contiennent actuellement des méthodes squelettes avec des logs pour tracer les actions, permettant de tester la structure sans implémentation complète.

## Auteur
Divin Koffi Sogbadji - TP2 Développement Mobile Automne 2025
