package com.example.tp2_divin_koffi_sogbadji.models

/**
 * Modèle de données représentant un article dans le magasin
 * 
 * @property id Identifiant unique de l'article (auto-généré par la base de données)
 * @property nom Nom de l'article
 * @property description Description détaillée de l'article
 * @property prix Prix unitaire de l'article en dollars
 * @property categorie Catégorie de l'article (Épicerie, Fruits, Légumes, etc.)
 * @property quantite Quantité disponible en stock (par défaut 1)
 */
data class Item(
    val id: Long = 0,
    val nom: String,
    val description: String,
    val prix: Double,
    val categorie: String,
    val quantite: Int = 1
) {
    /**
     * Retourne l'icône drawable correspondant à la catégorie
     */
    fun getCategoryIcon(): Int {
        return when (categorie.lowercase()) {
            "épicerie", "epicerie" -> com.example.tp2_divin_koffi_sogbadji.R.drawable.ic_category_epicerie
            "fruits" -> com.example.tp2_divin_koffi_sogbadji.R.drawable.ic_category_fruits
            "légumes", "legumes" -> com.example.tp2_divin_koffi_sogbadji.R.drawable.ic_category_legumes
            "boissons" -> com.example.tp2_divin_koffi_sogbadji.R.drawable.ic_category_boissons
            "viandes" -> com.example.tp2_divin_koffi_sogbadji.R.drawable.ic_category_viandes
            else -> com.example.tp2_divin_koffi_sogbadji.R.drawable.ic_default_item
        }
    }
    
    /**
     * Retourne le prix formaté en string
     */
    fun getPrixFormate(): String {
        return String.format("%.2f$", prix)
    }
    
    /**
     * Calcule le total pour une quantité donnée
     */
    fun calculerTotal(quantiteCommande: Int): Double {
        return prix * quantiteCommande
    }
    
    /**
     * Retourne le total formaté pour une quantité donnée
     */
    fun getTotalFormate(quantiteCommande: Int): String {
        return String.format("%.2f$", calculerTotal(quantiteCommande))
    }
}

/**
 * Modèle de données représentant un article dans le panier
 * 
 * @property item L'article du magasin
 * @property quantiteCommande La quantité commandée par le client
 */
data class ItemPanier(
    val item: Item,
    var quantiteCommande: Int
) {
    /**
     * Calcule le total pour cet item dans le panier
     */
    fun getTotal(): Double {
        return item.calculerTotal(quantiteCommande)
    }
    
    /**
     * Retourne le total formaté
     */
    fun getTotalFormate(): String {
        return item.getTotalFormate(quantiteCommande)
    }
}

/**
 * Enum représentant les catégories disponibles
 */
enum class Categorie(val displayName: String) {
    EPICERIE("Épicerie"),
    FRUITS("Fruits"),
    LEGUMES("Légumes"),
    BOISSONS("Boissons"),
    VIANDES("Viandes");
    
    companion object {
        /**
         * Retourne toutes les catégories sous forme de liste de strings
         */
        fun getAllCategories(): List<String> {
            return values().map { it.displayName }
        }
        
        /**
         * Trouve une catégorie par son nom d'affichage
         */
        fun fromDisplayName(displayName: String): Categorie? {
            return values().find { it.displayName == displayName }
        }
    }
}
