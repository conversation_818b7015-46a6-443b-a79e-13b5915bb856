<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Titre -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp"
        android:text="Choisir la quantité"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- Informations sur l'item -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ivDialogItemImage"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="12dp"
            android:contentDescription="Image de l'article"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_default_item" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogItemNom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="Nom de l'article" />

            <TextView
                android:id="@+id/tvDialogItemPrix"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/darker_gray"
                android:textSize="14sp"
                tools:text="12.99$ / unité" />

        </LinearLayout>

    </LinearLayout>

    <!-- Contrôles de quantité -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageButton
            android:id="@+id/btnDialogDiminuer"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Diminuer quantité"
            android:src="@drawable/ic_remove"
            app:tint="@color/design_default_color_primary" />

        <TextView
            android:id="@+id/tvDialogQuantite"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="1"
            android:textSize="24sp"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/btnDialogAugmenter"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Augmenter quantité"
            android:src="@drawable/ic_add"
            app:tint="@color/design_default_color_primary" />

    </LinearLayout>

    <!-- Total -->
    <TextView
        android:id="@+id/tvDialogTotal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="24dp"
        android:background="@drawable/bg_price_chip"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        android:text="Total: 12.99$"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- Boutons d'action -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnDialogAnnuler"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="Annuler" />

        <Button
            android:id="@+id/btnDialogAjouter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ajouter au panier" />

    </LinearLayout>

</LinearLayout>
