package com.example.tp2_divin_koffi_sogbadji.fragments

import androidx.fragment.app.Fragment

/**
 * Fragment de base pour tous les fragments de l'application
 * Cette classe sera étendue par MagasinFragment et PanierFragment
 * 
 * Pour l'instant, cette classe sert de placeholder en attendant d'apprendre
 * les concepts des fragments en cours.
 */
abstract class BaseFragment : Fragment() {
    
    /**
     * Tag pour les logs, sera surchargé par les classes filles
     */
    protected open val TAG: String = "BaseFragment"
    
    /**
     * Méthode abstraite que chaque fragment doit implémenter
     * pour définir son comportement spécifique
     */
    abstract fun setupFragment()
    
    /**
     * Méthode pour rafraîchir les données du fragment
     * À implémenter selon les besoins de chaque fragment
     */
    open fun refreshData() {
        // Implémentation par défaut vide
        // Les fragments enfants peuvent surcharger cette méthode
    }
    
    /**
     * Méthode appelée quand le mode admin change
     * @param isAdminMode true si le mode admin est activé
     */
    open fun onAdminModeChanged(isAdminMode: Boolean) {
        // Implémentation par défaut vide
        // Les fragments enfants peuvent surcharger cette méthode
    }
}

/**
 * Interface pour la communication entre les fragments et l'activité principale
 */
interface FragmentCallback {
    /**
     * Appelé quand un item est ajouté au panier
     * @param itemId ID de l'item ajouté
     * @param quantity Quantité ajoutée
     */
    fun onItemAddedToCart(itemId: Long, quantity: Int)
    
    /**
     * Appelé quand le total du panier change
     * @param newTotal Nouveau total du panier
     */
    fun onCartTotalChanged(newTotal: Double)
    
    /**
     * Appelé quand le mode admin change
     * @param isAdminMode true si le mode admin est activé
     */
    fun onAdminModeToggled(isAdminMode: Boolean)
}
