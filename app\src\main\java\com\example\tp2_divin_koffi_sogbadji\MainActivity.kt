package com.example.tp2_divin_koffi_sogbadji

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import com.example.tp2_divin_koffi_sogbadji.fragments.FragmentCallback
import com.example.tp2_divin_koffi_sogbadji.fragments.MagasinFragment
import com.example.tp2_divin_koffi_sogbadji.fragments.PanierFragment
import com.example.tp2_divin_koffi_sogbadji.utils.DataUtils
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.tabs.TabLayout

/**
 * Activité principale de l'application Panier Magasin
 *
 * Cette activité gère :
 * - La navigation par onglets entre Magasin et Panier
 * - Le menu principal avec l'option Admin
 * - La communication entre les fragments
 * - L'affichage du total du panier
 * - Le mode administrateur
 */
class MainActivity : AppCompatActivity(), FragmentCallback {

    private val TAG = "MainActivity"

    // Variables pour les vues (en attendant View Binding)
    private lateinit var toolbar: Toolbar
    private lateinit var tabLayout: TabLayout
    private lateinit var fabAddItem: FloatingActionButton
    private lateinit var tvTotal: TextView

    // Variables pour les fragments
    private lateinit var magasinFragment: MagasinFragment
    private lateinit var panierFragment: PanierFragment

    // Variables d'état
    private var isAdminMode = false
    private var currentTotal = 0.0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        setupViews()
        setupToolbar()
        setupTabs()
        setupFragments()

        android.util.Log.d(TAG, "MainActivity créée")
    }

    /**
     * Initialise les vues
     */
    private fun setupViews() {
        toolbar = findViewById(R.id.toolbar)
        tabLayout = findViewById(R.id.tabLayout)
        fabAddItem = findViewById(R.id.fabAddItem)
        tvTotal = findViewById(R.id.tvTotal)

        // Configuration du FAB
        fabAddItem.setOnClickListener {
            if (isAdminMode) {
                // TODO: Ouvrir le dialogue d'ajout d'article
                android.util.Log.d(TAG, "Clic sur FAB - Ajouter un article")
            }
        }
    }

    /**
     * Configure la toolbar
     */
    private fun setupToolbar() {
        setSupportActionBar(toolbar)
        supportActionBar?.title = getString(R.string.app_name)
    }

    /**
     * Configure les onglets
     */
    private fun setupTabs() {
        // Ajouter les onglets
        tabLayout.addTab(tabLayout.newTab().setText(R.string.tab_magasin))
        tabLayout.addTab(tabLayout.newTab().setText(R.string.tab_panier))

        // Listener pour les changements d'onglets
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                when (tab?.position) {
                    0 -> {
                        // Onglet Magasin sélectionné
                        showMagasinFragment()
                        // Afficher le FAB seulement en mode admin sur l'onglet magasin
                        fabAddItem.visibility = if (isAdminMode)
                            FloatingActionButton.VISIBLE else FloatingActionButton.GONE
                    }
                    1 -> {
                        // Onglet Panier sélectionné
                        showPanierFragment()
                        // Masquer le FAB sur l'onglet panier
                        fabAddItem.visibility = FloatingActionButton.GONE
                    }
                }
                android.util.Log.d(TAG, "Onglet sélectionné: ${tab?.position}")
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })

        // Sélectionner le premier onglet par défaut
        tabLayout.selectTab(tabLayout.getTabAt(0))
    }

    /**
     * Configure les fragments
     */
    private fun setupFragments() {
        magasinFragment = MagasinFragment.newInstance()
        panierFragment = PanierFragment.newInstance()

        // Définir les callbacks
        magasinFragment.setCallback(this)
        panierFragment.setCallback(this)

        // Afficher le fragment magasin par défaut
        showMagasinFragment()
    }

    /**
     * Affiche le fragment magasin
     */
    private fun showMagasinFragment() {
        // TODO: Remplacer par la gestion des fragments quand on aura appris
        // Pour l'instant, on simule juste l'affichage
        android.util.Log.d(TAG, "Affichage du fragment Magasin")
    }

    /**
     * Affiche le fragment panier
     */
    private fun showPanierFragment() {
        // TODO: Remplacer par la gestion des fragments quand on aura appris
        // Pour l'instant, on simule juste l'affichage
        android.util.Log.d(TAG, "Affichage du fragment Panier")
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_main, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_admin -> {
                toggleAdminMode()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * Bascule le mode administrateur
     */
    private fun toggleAdminMode() {
        isAdminMode = !isAdminMode

        // Mettre à jour l'affichage du FAB
        if (tabLayout.selectedTabPosition == 0) { // Onglet Magasin
            fabAddItem.visibility = if (isAdminMode)
                FloatingActionButton.VISIBLE else FloatingActionButton.GONE
        }

        // Notifier les fragments du changement de mode
        magasinFragment.onAdminModeChanged(isAdminMode)

        android.util.Log.d(TAG, "Mode admin basculé: $isAdminMode")
    }

    // Implémentation de FragmentCallback

    override fun onItemAddedToCart(itemId: Long, quantity: Int) {
        // TODO: Récupérer l'item depuis la base de données
        // Pour l'instant, on utilise les données de test
        val items = DataUtils.getItemsDeTest()
        val item = items.find { it.id == itemId }

        if (item != null) {
            panierFragment.addItemToPanier(item, quantity)
            android.util.Log.d(TAG, "Item ajouté au panier: ${item.nom} x$quantity")
        }
    }

    override fun onCartTotalChanged(newTotal: Double) {
        currentTotal = newTotal
        tvTotal.text = getString(R.string.total_format, newTotal)
        android.util.Log.d(TAG, "Total du panier mis à jour: ${DataUtils.formatPrice(newTotal)}")
    }

    override fun onAdminModeToggled(isAdminMode: Boolean) {
        this.isAdminMode = isAdminMode
        toggleAdminMode()
    }
}