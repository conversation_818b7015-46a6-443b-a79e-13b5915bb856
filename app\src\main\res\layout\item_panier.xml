<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- Image miniature de l'item -->
        <ImageView
            android:id="@+id/ivPanierItemImage"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="12dp"
            android:contentDescription="Image de l'article"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_default_item"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Nom de l'item -->
        <TextView
            android:id="@+id/tvPanierItemNom"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/layoutQuantite"
            app:layout_constraintStart_toEndOf="@+id/ivPanierItemImage"
            app:layout_constraintTop_toTopOf="@+id/ivPanierItemImage"
            tools:text="Nom de l'article" />

        <!-- Prix unitaire -->
        <TextView
            android:id="@+id/tvPanierItemPrixUnitaire"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:textColor="@android:color/darker_gray"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@+id/layoutQuantite"
            app:layout_constraintStart_toEndOf="@+id/ivPanierItemImage"
            app:layout_constraintTop_toBottomOf="@+id/tvPanierItemNom"
            tools:text="12.99$ / unité" />

        <!-- Layout pour la quantité et les contrôles -->
        <LinearLayout
            android:id="@+id/layoutQuantite"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toStartOf="@+id/tvPanierItemTotal"
            app:layout_constraintTop_toTopOf="@+id/ivPanierItemImage">

            <!-- Bouton diminuer quantité -->
            <ImageButton
                android:id="@+id/btnDiminuerQuantite"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Diminuer quantité"
                android:src="@drawable/ic_remove"
                app:tint="@color/design_default_color_primary" />

            <!-- Quantité -->
            <TextView
                android:id="@+id/tvQuantite"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="2" />

            <!-- Bouton augmenter quantité -->
            <ImageButton
                android:id="@+id/btnAugmenterQuantite"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Augmenter quantité"
                android:src="@drawable/ic_add"
                app:tint="@color/design_default_color_primary" />

        </LinearLayout>

        <!-- Total pour cet item -->
        <TextView
            android:id="@+id/tvPanierItemTotal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="@drawable/bg_price_chip"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivPanierItemImage"
            tools:text="25.98$" />

        <!-- Bouton supprimer -->
        <ImageButton
            android:id="@+id/btnSupprimerItem"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginTop="4dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Supprimer de panier"
            android:src="@drawable/ic_delete"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvPanierItemTotal"
            app:tint="@color/design_default_color_error" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
