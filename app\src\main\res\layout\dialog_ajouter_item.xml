<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- Titre -->
        <TextView
            android:id="@+id/tvDialogTitre"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="24dp"
            android:text="Ajouter un article"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- Nom de l'item -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Nom de l'article"
            app:boxStrokeColor="@color/design_default_color_primary"
            app:hintTextColor="@color/design_default_color_primary">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etNomItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textCapWords"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Description de l'item -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Description"
            app:boxStrokeColor="@color/design_default_color_primary"
            app:hintTextColor="@color/design_default_color_primary">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etDescriptionItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:maxLines="3" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Prix de l'item -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Prix (en $)"
            app:boxStrokeColor="@color/design_default_color_primary"
            app:hintTextColor="@color/design_default_color_primary"
            app:prefixText="$">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etPrixItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Catégorie de l'item -->
        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:hint="Catégorie"
            app:boxStrokeColor="@color/design_default_color_primary"
            app:hintTextColor="@color/design_default_color_primary">

            <AutoCompleteTextView
                android:id="@+id/actvCategorieItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Boutons d'action -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnDialogItemAnnuler"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="Annuler" />

            <Button
                android:id="@+id/btnDialogItemSauvegarder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Sauvegarder" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
