package com.example.tp2_divin_koffi_sogbadji.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.example.tp2_divin_koffi_sogbadji.R
import com.example.tp2_divin_koffi_sogbadji.models.Item
import com.example.tp2_divin_koffi_sogbadji.models.ItemPanier
import com.example.tp2_divin_koffi_sogbadji.utils.DataUtils

/**
 * Fragment qui affiche le contenu du panier d'achat
 * 
 * Ce fragment gère :
 * - L'affichage de la liste des articles dans le panier
 * - La modification des quantités
 * - La suppression d'articles du panier
 * - Le calcul et l'affichage du total
 * - Le bouton pour vider le panier
 */
class PanierFragment : BaseFragment() {
    
    override val TAG = "PanierFragment"
    
    // Variables pour gérer les données du panier
    private var panierItems = mutableListOf<ItemPanier>()
    private var callback: FragmentCallback? = null
    private var totalPanier = 0.0
    
    companion object {
        /**
         * Factory method pour créer une nouvelle instance du fragment
         */
        fun newInstance(): PanierFragment {
            return PanierFragment()
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Pour l'instant, on retourne juste le layout
        // Plus tard, on utilisera View Binding ici
        return inflater.inflate(R.layout.fragment_panier, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupFragment()
    }
    
    override fun setupFragment() {
        // TODO: Configurer le RecyclerView
        // TODO: Configurer l'adapter
        // TODO: Configurer le bouton "Vider le panier"
        // TODO: Configurer l'affichage du total
        
        updateTotal()
        android.util.Log.d(TAG, "Fragment Panier configuré")
    }
    
    override fun refreshData() {
        // Mettre à jour l'affichage du panier
        updateTotal()
        // TODO: Notifier l'adapter que les données ont changé
        android.util.Log.d(TAG, "Données du panier rafraîchies")
    }
    
    /**
     * Ajoute un article au panier ou augmente sa quantité s'il existe déjà
     */
    fun addItemToPanier(item: Item, quantity: Int) {
        val existingItem = panierItems.find { it.item.id == item.id }
        
        if (existingItem != null) {
            // L'article existe déjà, on augmente la quantité
            existingItem.quantiteCommande += quantity
        } else {
            // Nouvel article, on l'ajoute au panier
            panierItems.add(ItemPanier(item, quantity))
        }
        
        updateTotal()
        // TODO: Notifier l'adapter que les données ont changé
        android.util.Log.d(TAG, "Article ajouté au panier: ${item.nom} x$quantity")
    }
    
    /**
     * Met à jour la quantité d'un article dans le panier
     */
    private fun updateItemQuantity(itemPanier: ItemPanier, newQuantity: Int) {
        if (newQuantity <= 0) {
            // Si la quantité est 0 ou négative, on supprime l'article
            removeItemFromPanier(itemPanier)
        } else {
            itemPanier.quantiteCommande = newQuantity
            updateTotal()
            // TODO: Notifier l'adapter que l'item a changé
        }
        android.util.Log.d(TAG, "Quantité mise à jour: ${itemPanier.item.nom} x$newQuantity")
    }
    
    /**
     * Supprime un article du panier
     */
    private fun removeItemFromPanier(itemPanier: ItemPanier) {
        panierItems.remove(itemPanier)
        updateTotal()
        // TODO: Notifier l'adapter que l'item a été supprimé
        android.util.Log.d(TAG, "Article supprimé du panier: ${itemPanier.item.nom}")
    }
    
    /**
     * Vide complètement le panier
     */
    private fun clearPanier() {
        panierItems.clear()
        updateTotal()
        // TODO: Notifier l'adapter que toutes les données ont été supprimées
        android.util.Log.d(TAG, "Panier vidé")
    }
    
    /**
     * Met à jour le total du panier et notifie l'activité principale
     */
    private fun updateTotal() {
        totalPanier = panierItems.sumOf { it.getTotal() }
        callback?.onCartTotalChanged(totalPanier)
        
        // TODO: Mettre à jour l'affichage du total dans l'interface
        android.util.Log.d(TAG, "Total du panier mis à jour: ${DataUtils.formatPrice(totalPanier)}")
    }
    
    /**
     * Retourne le nombre total d'articles dans le panier
     */
    fun getTotalItemCount(): Int {
        return panierItems.sumOf { it.quantiteCommande }
    }
    
    /**
     * Retourne le total du panier
     */
    fun getTotalAmount(): Double {
        return totalPanier
    }
    
    /**
     * Vérifie si le panier est vide
     */
    fun isEmpty(): Boolean {
        return panierItems.isEmpty()
    }
    
    /**
     * Méthode appelée quand le bouton "+" est cliqué sur un item
     */
    private fun onIncreaseQuantityClicked(itemPanier: ItemPanier) {
        updateItemQuantity(itemPanier, itemPanier.quantiteCommande + 1)
    }
    
    /**
     * Méthode appelée quand le bouton "-" est cliqué sur un item
     */
    private fun onDecreaseQuantityClicked(itemPanier: ItemPanier) {
        updateItemQuantity(itemPanier, itemPanier.quantiteCommande - 1)
    }
    
    /**
     * Méthode appelée quand le bouton "supprimer" est cliqué sur un item
     */
    private fun onRemoveItemClicked(itemPanier: ItemPanier) {
        removeItemFromPanier(itemPanier)
    }
    
    /**
     * Méthode appelée quand le bouton "Vider le panier" est cliqué
     */
    private fun onClearCartClicked() {
        // TODO: Afficher une confirmation avant de vider
        clearPanier()
    }
    
    /**
     * Définit le callback pour communiquer avec l'activité principale
     */
    fun setCallback(callback: FragmentCallback) {
        this.callback = callback
    }
}
