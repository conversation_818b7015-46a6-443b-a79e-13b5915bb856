<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- Image de l'item (basée sur la catégorie) -->
        <ImageView
            android:id="@+id/ivItemImage"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="12dp"
            android:contentDescription="Image de l'article"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_default_item"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Nom de l'item -->
        <TextView
            android:id="@+id/tvItemNom"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/tvItemPrix"
            app:layout_constraintStart_toEndOf="@+id/ivItemImage"
            app:layout_constraintTop_toTopOf="@+id/ivItemImage"
            tools:text="Nom de l'article" />

        <!-- Description de l'item -->
        <TextView
            android:id="@+id/tvItemDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@android:color/darker_gray"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@+id/tvItemPrix"
            app:layout_constraintStart_toEndOf="@+id/ivItemImage"
            app:layout_constraintTop_toBottomOf="@+id/tvItemNom"
            tools:text="Description détaillée de l'article" />

        <!-- Catégorie de l'item -->
        <TextView
            android:id="@+id/tvItemCategorie"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/bg_category_chip"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/tvItemPrix"
            app:layout_constraintStart_toEndOf="@+id/ivItemImage"
            app:layout_constraintTop_toBottomOf="@+id/tvItemDescription"
            tools:text="Épicerie" />

        <!-- Prix de l'item -->
        <TextView
            android:id="@+id/tvItemPrix"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_price_chip"
            android:paddingHorizontal="12dp"
            android:paddingVertical="6dp"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivItemImage"
            tools:text="12.99$" />

        <!-- Indicateur de menu contextuel (visible seulement en mode admin) -->
        <ImageView
            android:id="@+id/ivMenuIndicator"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginTop="8dp"
            android:contentDescription="Menu contextuel"
            android:src="@drawable/ic_more_vert"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvItemPrix"
            app:tint="@android:color/darker_gray" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
