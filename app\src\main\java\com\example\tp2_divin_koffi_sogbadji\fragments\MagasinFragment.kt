package com.example.tp2_divin_koffi_sogbadji.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.example.tp2_divin_koffi_sogbadji.R
import com.example.tp2_divin_koffi_sogbadji.models.Item
import com.example.tp2_divin_koffi_sogbadji.utils.DataUtils

/**
 * Fragment qui affiche la liste des articles disponibles dans le magasin
 * 
 * Ce fragment gère :
 * - L'affichage de la liste des articles avec RecyclerView
 * - Le clic sur un article pour l'ajouter au panier
 * - Le mode administrateur (ajout/modification/suppression d'articles)
 * - Le menu contextuel en mode admin
 * - Le bouton flottant pour ajouter des articles en mode admin
 */
class MagasinFragment : BaseFragment() {
    
    override val TAG = "MagasinFragment"
    
    // Variables pour gérer les vues (seront initialisées avec View Binding plus tard)
    private var isAdminMode = false
    private var itemsList = mutableListOf<Item>()
    private var callback: FragmentCallback? = null
    
    companion object {
        /**
         * Factory method pour créer une nouvelle instance du fragment
         */
        fun newInstance(): MagasinFragment {
            return MagasinFragment()
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Pour l'instant, on retourne juste le layout
        // Plus tard, on utilisera View Binding ici
        return inflater.inflate(R.layout.fragment_magasin, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupFragment()
    }
    
    override fun setupFragment() {
        // Initialiser les données de test
        loadTestData()
        
        // TODO: Configurer le RecyclerView
        // TODO: Configurer l'adapter
        // TODO: Configurer les listeners de clic
        // TODO: Configurer le FloatingActionButton
        
        // Pour l'instant, on affiche juste un message dans les logs
        android.util.Log.d(TAG, "Fragment Magasin configuré avec ${itemsList.size} articles")
    }
    
    /**
     * Charge les données de test
     */
    private fun loadTestData() {
        itemsList.clear()
        itemsList.addAll(DataUtils.getItemsDeTest())
    }
    
    override fun refreshData() {
        // Recharger les données depuis la base de données
        // Pour l'instant, on recharge les données de test
        loadTestData()
        // TODO: Notifier l'adapter que les données ont changé
        android.util.Log.d(TAG, "Données du magasin rafraîchies")
    }
    
    override fun onAdminModeChanged(isAdminMode: Boolean) {
        this.isAdminMode = isAdminMode
        
        // TODO: Afficher/masquer le FloatingActionButton
        // TODO: Afficher/masquer les indicateurs de menu contextuel
        // TODO: Mettre à jour l'adapter pour gérer le mode admin
        
        android.util.Log.d(TAG, "Mode admin changé: $isAdminMode")
    }
    
    /**
     * Méthode appelée quand un article est cliqué
     */
    private fun onItemClicked(item: Item) {
        if (isAdminMode) {
            // En mode admin, afficher le menu contextuel
            showContextMenu(item)
        } else {
            // En mode normal, afficher le dialogue de quantité
            showQuantityDialog(item)
        }
    }
    
    /**
     * Affiche le dialogue pour choisir la quantité
     */
    private fun showQuantityDialog(item: Item) {
        // TODO: Implémenter le dialogue de quantité
        android.util.Log.d(TAG, "Affichage du dialogue de quantité pour: ${item.nom}")
    }
    
    /**
     * Affiche le menu contextuel pour un article (mode admin)
     */
    private fun showContextMenu(item: Item) {
        // TODO: Implémenter le menu contextuel
        android.util.Log.d(TAG, "Affichage du menu contextuel pour: ${item.nom}")
    }
    
    /**
     * Ajoute un article au panier
     */
    private fun addItemToCart(item: Item, quantity: Int) {
        callback?.onItemAddedToCart(item.id, quantity)
        android.util.Log.d(TAG, "Article ajouté au panier: ${item.nom} x$quantity")
    }
    
    /**
     * Affiche le dialogue pour ajouter un nouvel article (mode admin)
     */
    private fun showAddItemDialog() {
        // TODO: Implémenter le dialogue d'ajout d'article
        android.util.Log.d(TAG, "Affichage du dialogue d'ajout d'article")
    }
    
    /**
     * Affiche le dialogue pour modifier un article (mode admin)
     */
    private fun showEditItemDialog(item: Item) {
        // TODO: Implémenter le dialogue de modification d'article
        android.util.Log.d(TAG, "Affichage du dialogue de modification pour: ${item.nom}")
    }
    
    /**
     * Supprime un article (mode admin)
     */
    private fun deleteItem(item: Item) {
        // TODO: Implémenter la suppression d'article
        android.util.Log.d(TAG, "Suppression de l'article: ${item.nom}")
    }
    
    /**
     * Définit le callback pour communiquer avec l'activité principale
     */
    fun setCallback(callback: FragmentCallback) {
        this.callback = callback
    }
}
