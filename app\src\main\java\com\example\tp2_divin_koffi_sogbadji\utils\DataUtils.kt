package com.example.tp2_divin_koffi_sogbadji.utils

import com.example.tp2_divin_koffi_sogbadji.models.Item

/**
 * Classe utilitaire pour gérer les données de test et les opérations communes
 */
object DataUtils {
    
    /**
     * Génère une liste d'articles de test pour peupler le magasin
     * Cette méthode sera utilisée en attendant l'implémentation de la base de données
     */
    fun getItemsDeTest(): List<Item> {
        return listOf(
            Item(
                id = 1,
                nom = "Pommes Gala",
                description = "Pommes fraîches et croquantes, parfaites pour les collations",
                prix = 3.99,
                categorie = "Fruits",
                quantite = 1
            ),
            Item(
                id = 2,
                nom = "Lait 2%",
                description = "Lait frais 2% de matière grasse, 1 litre",
                prix = 4.29,
                categorie = "Épicerie",
                quantite = 1
            ),
            Item(
                id = 3,
                nom = "Brocoli",
                description = "Brocoli frais, riche en vitamines et minéraux",
                prix = 2.99,
                categorie = "Légumes",
                quantite = 1
            ),
            Item(
                id = 4,
                nom = "Coca-Cola",
                description = "Boisson gazeuse classique, canette 355ml",
                prix = 1.99,
                categorie = "Boissons",
                quantite = 1
            ),
            Item(
                id = 5,
                nom = "Poulet entier",
                description = "Poulet entier frais, environ 1.5kg",
                prix = 12.99,
                categorie = "Viandes",
                quantite = 1
            ),
            Item(
                id = 6,
                nom = "Bananes",
                description = "Bananes mûres et sucrées, parfaites pour le petit-déjeuner",
                prix = 2.49,
                categorie = "Fruits",
                quantite = 1
            ),
            Item(
                id = 7,
                nom = "Pain de blé entier",
                description = "Pain de blé entier tranché, riche en fibres",
                prix = 3.49,
                categorie = "Épicerie",
                quantite = 1
            ),
            Item(
                id = 8,
                nom = "Carottes",
                description = "Carottes fraîches, idéales pour les salades et cuissons",
                prix = 1.99,
                categorie = "Légumes",
                quantite = 1
            ),
            Item(
                id = 9,
                nom = "Jus d'orange",
                description = "Jus d'orange 100% pur, sans sucre ajouté, 1 litre",
                prix = 5.99,
                categorie = "Boissons",
                quantite = 1
            ),
            Item(
                id = 10,
                nom = "Bœuf haché",
                description = "Bœuf haché maigre, parfait pour les burgers et sauces",
                prix = 8.99,
                categorie = "Viandes",
                quantite = 1
            )
        )
    }
    
    /**
     * Formate un prix en string avec le symbole dollar
     */
    fun formatPrice(price: Double): String {
        return String.format("%.2f$", price)
    }
    
    /**
     * Calcule le total d'une liste d'items avec leurs quantités
     */
    fun calculateTotal(items: List<Pair<Item, Int>>): Double {
        return items.sumOf { (item, quantity) -> item.prix * quantity }
    }
    
    /**
     * Retourne le total formaté
     */
    fun getFormattedTotal(items: List<Pair<Item, Int>>): String {
        return formatPrice(calculateTotal(items))
    }
}
